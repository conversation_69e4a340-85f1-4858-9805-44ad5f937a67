{"name": "gloopi-storefront", "author": {"name": "Se<PERSON>", "url": "https://github.com/sesto-dev"}, "license": "MIT", "scripts": {"dev": "next dev -p 7777", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "postinstall": "prisma generate", "update": "npx npm-check-updates -u", "db:generate": "npx prisma generate", "db:format": "npx prisma format", "db:reset": "npx prisma db push --force-reset", "db:push": "npx prisma db push", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@persepolis/mail": "^1.0.9", "@persepolis/regex": "^0.0.3", "@persepolis/rng": "^0.0.1", "@persepolis/slugify": "^0.0.1", "@persepolis/sms": "^0.0.1", "@prisma/client": "^5.20.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@react-email/components": "^0.0.25", "@react-email/render": "^1.0.1", "@tanstack/react-table": "^8.20.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "jose": "^5.9.4", "lucide-react": "^0.452.0", "next": "14.2.15", "next-cloudinary": "^6.14.1", "next-mdx-remote": "^5.0.0", "next-themes": "^0.3.0", "nodemailer": "^6.9.15", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "swr": "^2.2.5", "zod": "^3.23.8"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/node": "^22.7.5", "@types/react": "^18.3.11", "autoprefixer": "^10.4.20", "eslint": "9.12.0", "eslint-config-next": "14.2.15", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "prisma": "^5.20.0", "tailwind-merge": "^2.5.3", "tailwindcss": "^3.4.13", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}